{"$message_type":"diagnostic","message":"unused import: `Manager`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\ctp_commands.rs","byte_start":32,"byte_end":39,"line_start":1,"line_end":1,"column_start":33,"column_end":40,"is_primary":true,"text":[{"text":"use tauri::{command, <PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, Emitter};","highlight_start":33,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\ctp_commands.rs","byte_start":30,"byte_end":39,"line_start":1,"line_end":1,"column_start":31,"column_end":40,"is_primary":true,"text":[{"text":"use tauri::{command, AppHandle, Manager, Emitter};","highlight_start":31,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Manager`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\ctp_commands.rs:1:33\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tauri::{command, AppHandle, Manager, Emitter};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type mismatch in function arguments","code":{"code":"E0631","explanation":"This error indicates a type mismatch in closure arguments.\n\nErroneous code example:\n\n```compile_fail,E0631\nfn foo<F: Fn(i32)>(f: F) {\n}\n\nfn main() {\n    foo(|x: &str| {});\n}\n```\n\nThe error occurs because `foo` accepts a closure that takes an `i32` argument,\nbut in `main`, it is passed a closure with a `&str` argument.\n\nThis can be resolved by changing the type annotation or removing it entirely\nif it can be inferred.\n\n```\nfn foo<F: Fn(i32)>(f: F) {\n}\n\nfn main() {\n    foo(|x: i32| {});\n}\n```\n"},"level":"error","spans":[{"file_name":"src\\ctp_commands.rs","byte_start":21515,"byte_end":21540,"line_start":584,"line_end":584,"column_start":37,"column_end":62,"is_primary":true,"text":[{"text":"        suggestions.into_iter().map(serde_json::Value::String).collect()","highlight_start":37,"highlight_end":62}],"label":"expected due to this","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\ctp_commands.rs","byte_start":21515,"byte_end":21540,"line_start":584,"line_end":584,"column_start":37,"column_end":62,"is_primary":true,"text":[{"text":"        suggestions.into_iter().map(serde_json::Value::String).collect()","highlight_start":37,"highlight_end":62}],"label":"found signature defined here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\ctp_commands.rs","byte_start":21511,"byte_end":21514,"line_start":584,"line_end":584,"column_start":33,"column_end":36,"is_primary":false,"text":[{"text":"        suggestions.into_iter().map(serde_json::Value::String).collect()","highlight_start":33,"highlight_end":36}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected function signature `fn(&str) -> _`\n   found function signature `fn(std::string::String) -> _`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `std::iter::Iterator::map`","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-msvc\\lib/rustlib/src/rust\\library\\core\\src\\iter\\traits\\iterator.rs","byte_start":25903,"byte_end":25906,"line_start":745,"line_end":745,"column_start":8,"column_end":11,"is_primary":false,"text":[{"text":"    fn map<B, F>(self, f: F) -> Map<Self, F>","highlight_start":8,"highlight_end":11}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-msvc\\lib/rustlib/src/rust\\library\\core\\src\\iter\\traits\\iterator.rs","byte_start":25983,"byte_end":26005,"line_start":748,"line_end":748,"column_start":12,"column_end":34,"is_primary":true,"text":[{"text":"        F: FnMut(Self::Item) -> B,","highlight_start":12,"highlight_end":34}],"label":"required by this bound in `Iterator::map`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"consider wrapping the function in a closure","code":null,"level":"help","spans":[{"file_name":"src\\ctp_commands.rs","byte_start":21515,"byte_end":21515,"line_start":584,"line_end":584,"column_start":37,"column_end":37,"is_primary":true,"text":[{"text":"        suggestions.into_iter().map(serde_json::Value::String).collect()","highlight_start":37,"highlight_end":37}],"label":null,"suggested_replacement":"|arg0: &str| ","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\ctp_commands.rs","byte_start":21540,"byte_end":21540,"line_start":584,"line_end":584,"column_start":62,"column_end":62,"is_primary":true,"text":[{"text":"        suggestions.into_iter().map(serde_json::Value::String).collect()","highlight_start":62,"highlight_end":62}],"label":null,"suggested_replacement":"(/* std::string::String */)","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0631]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: type mismatch in function arguments\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\ctp_commands.rs:584:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m584\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        suggestions.into_iter().map(serde_json::Value::String).collect()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected due to this\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfound signature defined here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: expected function signature `fn(\u001b[0m\u001b[0m\u001b[1m\u001b[35m&str\u001b[0m\u001b[0m) -> \u001b[0m\u001b[0m\u001b[1m\u001b[35m_\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m               found function signature `fn(\u001b[0m\u001b[0m\u001b[1m\u001b[35mstd::string::String\u001b[0m\u001b[0m) -> \u001b[0m\u001b[0m\u001b[1m\u001b[35m_\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `std::iter::Iterator::map`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-msvc\\lib/rustlib/src/rust\\library\\core\\src\\iter\\traits\\iterator.rs:748:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m745\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn map<B, F>(self, f: F) -> Map<Self, F>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m748\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        F: FnMut(Self::Item) -> B,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `Iterator::map`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider wrapping the function in a closure\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m584\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m        suggestions.into_iter().map(\u001b[0m\u001b[0m\u001b[38;5;10m|arg0: &str| \u001b[0m\u001b[0mserde_json::Value::String\u001b[0m\u001b[0m\u001b[38;5;10m(/* std::string::String */)\u001b[0m\u001b[0m).collect()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[38;5;10m++++++++++++\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[38;5;10m+++++++++++++++++++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the method `collect` exists for struct `Map<IntoIter<&str>, fn(String) -> Value {serde_json::Value::String}>`, but its trait bounds were not satisfied","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\ctp_commands.rs","byte_start":21542,"byte_end":21549,"line_start":584,"line_end":584,"column_start":64,"column_end":71,"is_primary":true,"text":[{"text":"        suggestions.into_iter().map(serde_json::Value::String).collect()","highlight_start":64,"highlight_end":71}],"label":"method cannot be called due to unsatisfied trait bounds","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-msvc\\lib/rustlib/src/rust\\library\\core\\src\\iter\\adapters\\map.rs","byte_start":2060,"byte_end":2080,"line_start":61,"line_end":61,"column_start":1,"column_end":21,"is_primary":false,"text":[{"text":"pub struct Map<I, F> {","highlight_start":1,"highlight_end":21}],"label":"doesn't satisfy `_: Iterator`, `_: StreamExt` or `_: Stream`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following trait bounds were not satisfied:\n`std::iter::Map<std::vec::IntoIter<&str>, fn(std::string::String) -> serde_json::Value {serde_json::Value::String}>: Stream`\nwhich is required by `std::iter::Map<std::vec::IntoIter<&str>, fn(std::string::String) -> serde_json::Value {serde_json::Value::String}>: StreamExt`\n`<fn(std::string::String) -> serde_json::Value {serde_json::Value::String} as FnOnce<(&str,)>>::Output = _`\nwhich is required by `std::iter::Map<std::vec::IntoIter<&str>, fn(std::string::String) -> serde_json::Value {serde_json::Value::String}>: Iterator`\n`fn(std::string::String) -> serde_json::Value {serde_json::Value::String}: FnMut<(&str,)>`\nwhich is required by `std::iter::Map<std::vec::IntoIter<&str>, fn(std::string::String) -> serde_json::Value {serde_json::Value::String}>: Iterator`\n`&std::iter::Map<std::vec::IntoIter<&str>, fn(std::string::String) -> serde_json::Value {serde_json::Value::String}>: Stream`\nwhich is required by `&std::iter::Map<std::vec::IntoIter<&str>, fn(std::string::String) -> serde_json::Value {serde_json::Value::String}>: StreamExt`\n`&mut std::iter::Map<std::vec::IntoIter<&str>, fn(std::string::String) -> serde_json::Value {serde_json::Value::String}>: Stream`\nwhich is required by `&mut std::iter::Map<std::vec::IntoIter<&str>, fn(std::string::String) -> serde_json::Value {serde_json::Value::String}>: StreamExt`\n`std::iter::Map<std::vec::IntoIter<&str>, fn(std::string::String) -> serde_json::Value {serde_json::Value::String}>: Iterator`\nwhich is required by `&mut std::iter::Map<std::vec::IntoIter<&str>, fn(std::string::String) -> serde_json::Value {serde_json::Value::String}>: Iterator`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the method `collect` exists for struct `Map<IntoIter<&str>, fn(String) -> Value {serde_json::Value::String}>`, but its trait bounds were not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\ctp_commands.rs:584:64\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m584\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        suggestions.into_iter().map(serde_json::Value::String).collect()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod cannot be called due to unsatisfied trait bounds\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mC:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-msvc\\lib/rustlib/src/rust\\library\\core\\src\\iter\\adapters\\map.rs:61:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m61\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct Map<I, F> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mdoesn't satisfy `_: Iterator`, `_: StreamExt` or `_: Stream`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the following trait bounds were not satisfied:\u001b[0m\n\u001b[0m            `std::iter::Map<std::vec::IntoIter<&str>, fn(std::string::String) -> serde_json::Value {serde_json::Value::String}>: Stream`\u001b[0m\n\u001b[0m            which is required by `std::iter::Map<std::vec::IntoIter<&str>, fn(std::string::String) -> serde_json::Value {serde_json::Value::String}>: StreamExt`\u001b[0m\n\u001b[0m            `<fn(std::string::String) -> serde_json::Value {serde_json::Value::String} as FnOnce<(&str,)>>::Output = _`\u001b[0m\n\u001b[0m            which is required by `std::iter::Map<std::vec::IntoIter<&str>, fn(std::string::String) -> serde_json::Value {serde_json::Value::String}>: Iterator`\u001b[0m\n\u001b[0m            `fn(std::string::String) -> serde_json::Value {serde_json::Value::String}: FnMut<(&str,)>`\u001b[0m\n\u001b[0m            which is required by `std::iter::Map<std::vec::IntoIter<&str>, fn(std::string::String) -> serde_json::Value {serde_json::Value::String}>: Iterator`\u001b[0m\n\u001b[0m            `&std::iter::Map<std::vec::IntoIter<&str>, fn(std::string::String) -> serde_json::Value {serde_json::Value::String}>: Stream`\u001b[0m\n\u001b[0m            which is required by `&std::iter::Map<std::vec::IntoIter<&str>, fn(std::string::String) -> serde_json::Value {serde_json::Value::String}>: StreamExt`\u001b[0m\n\u001b[0m            `&mut std::iter::Map<std::vec::IntoIter<&str>, fn(std::string::String) -> serde_json::Value {serde_json::Value::String}>: Stream`\u001b[0m\n\u001b[0m            which is required by `&mut std::iter::Map<std::vec::IntoIter<&str>, fn(std::string::String) -> serde_json::Value {serde_json::Value::String}>: StreamExt`\u001b[0m\n\u001b[0m            `std::iter::Map<std::vec::IntoIter<&str>, fn(std::string::String) -> serde_json::Value {serde_json::Value::String}>: Iterator`\u001b[0m\n\u001b[0m            which is required by `&mut std::iter::Map<std::vec::IntoIter<&str>, fn(std::string::String) -> serde_json::Value {serde_json::Value::String}>: Iterator`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of moved value: `order_result.error`","code":{"code":"E0382","explanation":"A variable was used after its contents have been moved elsewhere.\n\nErroneous code example:\n\n```compile_fail,E0382\nstruct MyStruct { s: u32 }\n\nfn main() {\n    let mut x = MyStruct{ s: 5u32 };\n    let y = x;\n    x.s = 6;\n    println!(\"{}\", x.s);\n}\n```\n\nSince `MyStruct` is a type that is not marked `Copy`, the data gets moved out\nof `x` when we set `y`. This is fundamental to Rust's ownership system: outside\nof workarounds like `Rc`, a value cannot be owned by more than one variable.\n\nSometimes we don't need to move the value. Using a reference, we can let another\nfunction borrow the value without changing its ownership. In the example below,\nwe don't actually have to move our string to `calculate_length`, we can give it\na reference to it with `&` instead.\n\n```\nfn main() {\n    let s1 = String::from(\"hello\");\n\n    let len = calculate_length(&s1);\n\n    println!(\"The length of '{}' is {}.\", s1, len);\n}\n\nfn calculate_length(s: &String) -> usize {\n    s.len()\n}\n```\n\nA mutable reference can be created with `&mut`.\n\nSometimes we don't want a reference, but a duplicate. All types marked `Clone`\ncan be duplicated by calling `.clone()`. Subsequent changes to a clone do not\naffect the original variable.\n\nMost types in the standard library are marked `Clone`. The example below\ndemonstrates using `clone()` on a string. `s1` is first set to \"many\", and then\ncopied to `s2`. Then the first character of `s1` is removed, without affecting\n`s2`. \"any many\" is printed to the console.\n\n```\nfn main() {\n    let mut s1 = String::from(\"many\");\n    let s2 = s1.clone();\n    s1.remove(0);\n    println!(\"{} {}\", s1, s2);\n}\n```\n\nIf we control the definition of a type, we can implement `Clone` on it ourselves\nwith `#[derive(Clone)]`.\n\nSome types have no ownership semantics at all and are trivial to duplicate. An\nexample is `i32` and the other number types. We don't have to call `.clone()` to\nclone them, because they are marked `Copy` in addition to `Clone`. Implicit\ncloning is more convenient in this case. We can mark our own types `Copy` if\nall their members also are marked `Copy`.\n\nIn the example below, we implement a `Point` type. Because it only stores two\nintegers, we opt-out of ownership semantics with `Copy`. Then we can\n`let p2 = p1` without `p1` being moved.\n\n```\n#[derive(Copy, Clone)]\nstruct Point { x: i32, y: i32 }\n\nfn main() {\n    let mut p1 = Point{ x: -1, y: 2 };\n    let p2 = p1;\n    p1.x = 1;\n    println!(\"p1: {}, {}\", p1.x, p1.y);\n    println!(\"p2: {}, {}\", p2.x, p2.y);\n}\n```\n\nAlternatively, if we don't control the struct's definition, or mutable shared\nownership is truly required, we can use `Rc` and `RefCell`:\n\n```\nuse std::cell::RefCell;\nuse std::rc::Rc;\n\nstruct MyStruct { s: u32 }\n\nfn main() {\n    let mut x = Rc::new(RefCell::new(MyStruct{ s: 5u32 }));\n    let y = x.clone();\n    x.borrow_mut().s = 6;\n    println!(\"{}\", x.borrow().s);\n}\n```\n\nWith this approach, x and y share ownership of the data via the `Rc` (reference\ncount type). `RefCell` essentially performs runtime borrow checking: ensuring\nthat at most one writer or multiple readers can access the data at any one time.\n\nIf you wish to learn more about ownership in Rust, start with the\n[Understanding Ownership][understanding-ownership] chapter in the Book.\n\n[understanding-ownership]: https://doc.rust-lang.org/book/ch04-00-understanding-ownership.html\n"},"level":"error","spans":[{"file_name":"src\\ctp_commands.rs","byte_start":29024,"byte_end":29042,"line_start":772,"line_end":772,"column_start":13,"column_end":31,"is_primary":false,"text":[{"text":"            order_result.error.unwrap_or(\"订单提交失败\".to_string())","highlight_start":13,"highlight_end":31}],"label":"value moved here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\ctp_commands.rs","byte_start":29547,"byte_end":29565,"line_start":783,"line_end":783,"column_start":13,"column_end":31,"is_primary":true,"text":[{"text":"            order_result.error.unwrap_or(\"未知错误\".to_string())","highlight_start":13,"highlight_end":31}],"label":"value used here after move","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"move occurs because `order_result.error` has type `std::option::Option<std::string::String>`, which does not implement the `Copy` trait","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0382]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of moved value: `order_result.error`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\ctp_commands.rs:783:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m772\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            order_result.error.unwrap_or(\"订单提交失败\".to_string())\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mvalue moved here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m783\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            order_result.error.unwrap_or(\"未知错误\".to_string())\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mvalue used here after move\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: move occurs because `order_result.error` has type `std::option::Option<std::string::String>`, which does not implement the `Copy` trait\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `e`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\ctp_commands.rs","byte_start":38126,"byte_end":38127,"line_start":1041,"line_end":1041,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"            if let Err(e) = std::fs::create_dir_all(parent) {","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\ctp_commands.rs","byte_start":38126,"byte_end":38127,"line_start":1041,"line_end":1041,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"            if let Err(e) = std::fs::create_dir_all(parent) {","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":"_e","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `e`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\ctp_commands.rs:1041:24\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1041\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            if let Err(e) = std::fs::create_dir_all(parent) {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_e`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `packet`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\ctp_commands.rs","byte_start":58738,"byte_end":58744,"line_start":1473,"line_end":1473,"column_start":68,"column_end":74,"is_primary":true,"text":[{"text":"                        CThostFtdcMdSpiOutput::OnFrontDisconnected(packet) => {","highlight_start":68,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\ctp_commands.rs","byte_start":58738,"byte_end":58744,"line_start":1473,"line_end":1473,"column_start":68,"column_end":74,"is_primary":true,"text":[{"text":"                        CThostFtdcMdSpiOutput::OnFrontDisconnected(packet) => {","highlight_start":68,"highlight_end":74}],"label":null,"suggested_replacement":"_packet","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `packet`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\ctp_commands.rs:1473:68\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1473\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        CThostFtdcMdSpiOutput::OnFrontDisconnected(packet) => {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_packet`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `instruments_count`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\ctp_commands.rs","byte_start":64297,"byte_end":64314,"line_start":1574,"line_end":1574,"column_start":13,"column_end":30,"is_primary":true,"text":[{"text":"        let instruments_count = {","highlight_start":13,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\ctp_commands.rs","byte_start":64297,"byte_end":64314,"line_start":1574,"line_end":1574,"column_start":13,"column_end":30,"is_primary":true,"text":[{"text":"        let instruments_count = {","highlight_start":13,"highlight_end":30}],"label":null,"suggested_replacement":"_instruments_count","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `instruments_count`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\ctp_commands.rs:1574:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1574\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let instruments_count = {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_instruments_count`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `session_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\ctp_commands.rs","byte_start":64627,"byte_end":64637,"line_start":1584,"line_end":1584,"column_start":101,"column_end":111,"is_primary":true,"text":[{"text":"fn handle_ctp_market_data_callback(market_data: &tauri_app_vue_lib::CThostFtdcDepthMarketDataField, session_id: &str) {","highlight_start":101,"highlight_end":111}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\ctp_commands.rs","byte_start":64627,"byte_end":64637,"line_start":1584,"line_end":1584,"column_start":101,"column_end":111,"is_primary":true,"text":[{"text":"fn handle_ctp_market_data_callback(market_data: &tauri_app_vue_lib::CThostFtdcDepthMarketDataField, session_id: &str) {","highlight_start":101,"highlight_end":111}],"label":null,"suggested_replacement":"_session_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `session_id`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\ctp_commands.rs:1584:101\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1584\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn handle_ctp_market_data_callback(market_data: &tauri_app_vue_lib::CThostFtdcDepthMarketDataField, session_id: &str) {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_session_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 3 previous errors; 5 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 3 previous errors; 5 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0382, E0599, E0631.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0382, E0599, E0631.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0382`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0382`.\u001b[0m\n"}
